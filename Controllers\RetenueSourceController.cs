using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.Services;
using System.Security.Claims;

namespace stage.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class RetenueSourceController : ControllerBase
    {
        private readonly IRetenueSourceRepository _retenueSourceRepository;
        private readonly IUtilisateurRepository _utilisateurRepository;
        private readonly ILogger<RetenueSourceController> _logger;

        public RetenueSourceController(
            IRetenueSourceRepository retenueSourceRepository,
            IUtilisateurRepository utilisateurRepository,
            ILogger<RetenueSourceController> logger)
        {
            _retenueSourceRepository = retenueSourceRepository;
            _utilisateurRepository = utilisateurRepository;
            _logger = logger;
        }

        private async Task<Guid?> GetUserSocieteIdAsync()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                return null;

            var utilisateur = await _utilisateurRepository.GetByIdAsync(userId);
            return utilisateur?.SocieteId;
        }

        private async Task<Guid?> GetUserIdAsync()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
                return null;

            return userId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<RetenueSource>>> GetAll()
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenues = await _retenueSourceRepository.GetBySocieteIdAsync(societeId.Value);
                return Ok(retenues);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des retenues à la source");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<RetenueSource>> GetById(Guid id)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenue = await _retenueSourceRepository.GetWithDocumentsAsync(id);
                if (retenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                // Vérifier que la retenue appartient à la société de l'utilisateur
                if (retenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                return Ok(retenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpPost]
        public async Task<ActionResult<RetenueSource>> Create([FromBody] RetenueSource retenue)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                var userId = await GetUserIdAsync();
                
                if (societeId == null || userId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                // Vérifier si le numéro existe déjà pour cette société
                var existingRetenue = await _retenueSourceRepository.GetByNumeroAsync(retenue.Numero, societeId.Value);
                if (existingRetenue != null)
                    return BadRequest("Une retenue avec ce numéro existe déjà.");

                // Assigner la société et l'utilisateur
                retenue.SocieteId = societeId.Value;
                retenue.UtilisateurId = userId.Value;
                retenue.DateCreation = DateTime.UtcNow;

                // Calculer le montant retenu et net si pas déjà fait
                if (retenue.MontantRetenu == 0)
                    retenue.MontantRetenu = retenue.MontantBrut * (retenue.TauxRetenue / 100);
                
                if (retenue.MontantNet == 0)
                    retenue.MontantNet = retenue.MontantBrut - retenue.MontantRetenu;

                var createdRetenue = await _retenueSourceRepository.AddAsync(retenue);
                return CreatedAtAction(nameof(GetById), new { id = createdRetenue.Id }, createdRetenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la retenue à la source");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<RetenueSource>> Update(Guid id, [FromBody] RetenueSource retenue)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var existingRetenue = await _retenueSourceRepository.GetByIdAsync(id);
                if (existingRetenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                // Vérifier que la retenue appartient à la société de l'utilisateur
                if (existingRetenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                // Mettre à jour les propriétés
                existingRetenue.Numero = retenue.Numero;
                existingRetenue.DateRetenue = retenue.DateRetenue;
                existingRetenue.MontantBrut = retenue.MontantBrut;
                existingRetenue.TauxRetenue = retenue.TauxRetenue;
                existingRetenue.Description = retenue.Description;
                existingRetenue.Beneficiaire = retenue.Beneficiaire;
                existingRetenue.MatriculeFiscaleBeneficiaire = retenue.MatriculeFiscaleBeneficiaire;
                existingRetenue.DateModification = DateTime.UtcNow;

                // Recalculer les montants
                existingRetenue.MontantRetenu = existingRetenue.MontantBrut * (existingRetenue.TauxRetenue / 100);
                existingRetenue.MontantNet = existingRetenue.MontantBrut - existingRetenue.MontantRetenu;

                var updatedRetenue = await _retenueSourceRepository.UpdateAsync(existingRetenue);
                return Ok(updatedRetenue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la mise à jour de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenue = await _retenueSourceRepository.GetByIdAsync(id);
                if (retenue == null)
                    return NotFound("Retenue à la source non trouvée.");

                // Vérifier que la retenue appartient à la société de l'utilisateur
                if (retenue.SocieteId != societeId.Value)
                    return Forbid("Accès non autorisé à cette retenue.");

                await _retenueSourceRepository.DeleteAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de la retenue {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("scanned")]
        public async Task<ActionResult<IEnumerable<RetenueSource>>> GetScannedOrUploaded()
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenues = await _retenueSourceRepository.GetScannedOrUploadedAsync(societeId.Value);
                return Ok(retenues);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des retenues scannées/uploadées");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("date-range")]
        public async Task<ActionResult<IEnumerable<RetenueSource>>> GetByDateRange(
            [FromQuery] DateTime dateDebut,
            [FromQuery] DateTime dateFin)
        {
            try
            {
                var societeId = await GetUserSocieteIdAsync();
                if (societeId == null)
                    return Unauthorized("Utilisateur non trouvé ou société non associée.");

                var retenues = await _retenueSourceRepository.GetByDateRangeAsync(societeId.Value, dateDebut, dateFin);
                return Ok(retenues);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des retenues par période");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        // TODO: Endpoints pour upload/scan de documents
        // [HttpPost("{id}/upload-document")]
        // [HttpPost("{id}/scan-document")]
        // Ces endpoints seront implémentés dans la prochaine étape
    }
}
