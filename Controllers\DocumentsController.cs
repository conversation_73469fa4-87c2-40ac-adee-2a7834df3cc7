﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore; 

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DocumentsController : ControllerBase
    {
        private readonly IDocumentRepository _documentRepository;
        private readonly ITypeDocumentRepository _typeDocumentRepository;
 

        public DocumentsController(IDocumentRepository documentRepository,
                                   ITypeDocumentRepository typeDocumentRepository)
        {
            _documentRepository = documentRepository;
            _typeDocumentRepository = typeDocumentRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
            
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT. User not properly authenticated.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Document>>> GetDocuments()
        {
            var societeId = GetUserSocieteId();
           
            var documents = await _documentRepository.GetQueryable()
                                                     .Where(d => d.SocieteId == societeId)
                                                     .Include(d => d.TypeDocument)
                                                     .Include(d => d.Societe) 
                                                     .ToListAsync();
            return Ok(documents);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Document>> GetDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
        
            var document = await _documentRepository.GetQueryable()
                                                     .Include(d => d.TypeDocument)
                                                     .Include(d => d.Societe) 
                                                     .FirstOrDefaultAsync(d => d.Id == id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(document);
        }

        [HttpPost]
        public async Task<ActionResult<Document>> PostDocument([FromBody] Document document)
        {
            
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

          
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

          
            document.Id = Guid.NewGuid();
            document.SocieteId = societeId; 
            document.DateCreation = DateTime.UtcNow; 

            
            document.TypeDocument = null;
            document.Societe = null;

            await _documentRepository.AddAsync(document);
            await _documentRepository.SaveChangesAsync();

           
            var createdDocument = await _documentRepository.GetQueryable()
                                                            .Include(d => d.TypeDocument)
                                                            .Include(d => d.Societe)
                                                            .FirstOrDefaultAsync(d => d.Id == document.Id);

            return CreatedAtAction(nameof(GetDocument), new { id = createdDocument.Id }, createdDocument);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutDocument(Guid id, [FromBody] Document document)
        {
            
            if (id != document.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingDocument = await _documentRepository.GetByIdAsync(id);

            
            if (existingDocument == null || existingDocument.SocieteId != societeId)
            {
                return NotFound();
            }

          
            var typeDoc = await _typeDocumentRepository.GetByIdAsync(document.TypeDocumentId);
            if (typeDoc == null || typeDoc.SocieteId != societeId)
            {
                return BadRequest("Le type de document spécifié n'existe pas ou n'appartient pas à votre société.");
            }

        
            existingDocument.Titre = document.Titre;
            existingDocument.Contenu = document.Contenu;
            existingDocument.TypeDocumentId = document.TypeDocumentId;
           

            _documentRepository.Update(existingDocument);

            try
            {
                await _documentRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await DocumentExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteDocument(Guid id)
        {
            var societeId = GetUserSocieteId();
            var document = await _documentRepository.GetByIdAsync(id);

            if (document == null || document.SocieteId != societeId)
            {
                return NotFound();
            }

            _documentRepository.Remove(document);
            await _documentRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> DocumentExists(Guid id, Guid societeId)
        {
           
            return (await _documentRepository.FindAsync(d => d.Id == id && d.SocieteId == societeId)).Any();
        }
    }
}