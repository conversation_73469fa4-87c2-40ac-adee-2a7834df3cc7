﻿using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.DTOs.User;
using stage.Services;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Admin")] 
    public class UtilisateursController : ControllerBase
    {
        private readonly UserManager<Applicationuser> _userManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager;
        private readonly ISocieteRepository _societeRepository;
        private readonly IRoleService _roleService;

        public UtilisateursController(UserManager<Applicationuser> userManager,
                                      RoleManager<IdentityRole<Guid>> roleManager,
                                      ISocieteRepository societeRepository,
                                      IRoleService roleService)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _societeRepository = societeRepository;
            _roleService = roleService;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        [HttpGet("roles")]
        public async Task<ActionResult<List<string>>> GetAvailableRoles()
        {
            var roles = await _roleService.GetAvailableRolesAsync();
            return Ok(roles);
        }

        private async Task<UtilisateurDto> MapToUtilisateurDto(Applicationuser user)
        {
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);

            
            var societe = user.Societe;
            if (societe == null)
            {
                societe = await _societeRepository.GetByIdAsync(user.SocieteId);
            }

            return new UtilisateurDto
            {
                Id = user.Id,
                Nom = user.Nom,
                Email = user.Email,
                Role = roles.FirstOrDefault() ?? "N/A", 
                SocieteId = user.SocieteId,
                SocieteNom = societe?.Nom 
            };
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<UtilisateurDto>>> GetUtilisateurs()
        {
            var societeId = GetUserSocieteId();
            var users = await _userManager.Users
                                          .Where(u => u.SocieteId == societeId)
                                          .Include(u => u.Societe)
                                          .ToListAsync();

            var userDtos = new List<UtilisateurDto>();
            foreach (var user in users)
            {
                userDtos.Add(await MapToUtilisateurDto(user));
            }
            return Ok(userDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<UtilisateurDto>> GetUtilisateur(Guid id)
        {
            var societeId = GetUserSocieteId();
            var user = await _userManager.Users
                                         .Include(u => u.Societe)
                                         .FirstOrDefaultAsync(u => u.Id == id);

            if (user == null || user.SocieteId != societeId)
            {
                return NotFound(); 
            }

            return Ok(await MapToUtilisateurDto(user));
        }

        [HttpPost]
        public async Task<ActionResult<UtilisateurDto>> PostUtilisateur([FromBody] UtilisateurCreateDto utilisateurCreateDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            var societeExists = await _societeRepository.GetByIdAsync(societeId);
            if (societeExists == null)
            {
                return BadRequest("La société associée n'existe pas.");
            }

            if (await _userManager.FindByEmailAsync(utilisateurCreateDto.Email) != null)
            {
                return Conflict("Un utilisateur avec cet email existe déjà.");
            }

            var roleExists = await _roleManager.RoleExistsAsync(utilisateurCreateDto.Role);
            if (!roleExists)
            {
                return BadRequest($"Le rôle '{utilisateurCreateDto.Role}' n'existe pas. Veuillez utiliser un rôle existant (ex: Admin, User).");
            }

            var newUser = new Applicationuser
            {
                Id = Guid.NewGuid(),
                Nom = utilisateurCreateDto.Nom,
                Email = utilisateurCreateDto.Email,
                UserName = utilisateurCreateDto.Email, 
                SocieteId = societeId
            };

            var createResult = await _userManager.CreateAsync(newUser, utilisateurCreateDto.MotDePasse);
            if (!createResult.Succeeded)
            {
                return BadRequest(createResult.Errors.Select(e => e.Description));
            }

            var addRoleResult = await _userManager.AddToRoleAsync(newUser, utilisateurCreateDto.Role);
            if (!addRoleResult.Succeeded)
            {
                await _userManager.DeleteAsync(newUser); 
                return BadRequest(addRoleResult.Errors.Select(e => e.Description));
            }

            newUser.Societe = societeExists;

            return CreatedAtAction(nameof(GetUtilisateur), new { id = newUser.Id }, await MapToUtilisateurDto(newUser));
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutUtilisateur(Guid id, [FromBody] UtilisateurUpdateDto utilisateurUpdateDto)
        {
            if (id != utilisateurUpdateDto.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingUser = await _userManager.Users.FirstOrDefaultAsync(u => u.Id == id);

            if (existingUser == null || existingUser.SocieteId != societeId)
            {
                return NotFound(); 
            }

            existingUser.Nom = utilisateurUpdateDto.Nom;
            existingUser.Email = utilisateurUpdateDto.Email;
            existingUser.UserName = utilisateurUpdateDto.Email; 
            if (!string.IsNullOrEmpty(utilisateurUpdateDto.MotDePasse))
            {
                var token = await _userManager.GeneratePasswordResetTokenAsync(existingUser);
                var resetPasswordResult = await _userManager.ResetPasswordAsync(existingUser, token, utilisateurUpdateDto.MotDePasse);
                if (!resetPasswordResult.Succeeded) return BadRequest(resetPasswordResult.Errors);
            }

            var currentRoles = await _userManager.GetRolesAsync(existingUser);
            var newRole = utilisateurUpdateDto.Role;

            var roleExists = await _roleManager.RoleExistsAsync(newRole);
            if (!roleExists)
            {
                return BadRequest($"Le rôle '{newRole}' n'existe pas. Veuillez utiliser un rôle existant (ex: Admin, User).");
            }
            if (!currentRoles.Contains(newRole))
            {
                var removeRolesResult = await _userManager.RemoveFromRolesAsync(existingUser, currentRoles);
                if (!removeRolesResult.Succeeded) return BadRequest(removeRolesResult.Errors.Select(e => e.Description));

                var addRoleResult = await _userManager.AddToRoleAsync(existingUser, newRole);
                if (!addRoleResult.Succeeded) return BadRequest(addRoleResult.Errors.Select(e => e.Description));
            }

            var updateResult = await _userManager.UpdateAsync(existingUser);
            if (!updateResult.Succeeded)
            {
                return BadRequest(updateResult.Errors.Select(e => e.Description));
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUtilisateur(Guid id)
        {
            var societeId = GetUserSocieteId();
            var user = await _userManager.FindByIdAsync(id.ToString());

            if (user == null || user.SocieteId != societeId)
            {
                return NotFound(); 
            }

            if (User.FindFirst(ClaimTypes.NameIdentifier)?.Value == id.ToString())
            {
                return BadRequest("Un administrateur ne peut pas supprimer son propre compte via cette API.");
            }

            var deleteResult = await _userManager.DeleteAsync(user);
            if (!deleteResult.Succeeded)
            {
                return BadRequest(deleteResult.Errors.Select(e => e.Description));
            }

            return NoContent();
        }
    }
}