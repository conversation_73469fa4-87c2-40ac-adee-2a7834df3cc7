using System.ComponentModel.DataAnnotations;

namespace stage.Models
{
   
    public class RetenueSource
    {
        public Guid Id { get; set; }
        
        // Informations du fichier scanné/uploadé (SEULEMENT date et chemin)
      public DateTime? DateScan { get; set; }

      [MaxLength(500)]
     public string? CheminFichier { get; set; }
        
        // Relations
        [Required]
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        
        [Required]
        public Guid UtilisateurId { get; set; }
        public Utilisateur Utilisateur { get; set; }
        
        // Commentaires et notes
        public string? Commentaires { get; set; }
    }
}
