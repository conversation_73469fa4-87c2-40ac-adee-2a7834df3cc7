using System.ComponentModel.DataAnnotations;

namespace stage.Models
{
    public enum TypeRetenue
    {
        <PERSON><PERSON>,
        Honoraires,
        Loyer,
        Prestation,
        Autre
    }

    public enum StatutRetenue
    {
        EnCours,
        Valide,
        Archive
    }

    public class RetenueSource
    {
        public Guid Id { get; set; }
        
        [Required]
        [MaxLength(100)]
        public string Numero { get; set; }
        
        [Required]
        public DateTime DateRetenue { get; set; }
        
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Le montant doit être positif")]
        public decimal MontantBrut { get; set; }
        
        [Required]
        [Range(0, 100, ErrorMessage = "Le taux doit être entre 0 et 100")]
        public decimal TauxRetenue { get; set; }
        
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Le montant retenu doit être positif")]
        public decimal MontantRetenu { get; set; }
        
        [Required]
        [Range(0, double.MaxValue, ErrorMessage = "Le montant net doit être positif")]
        public decimal MontantNet { get; set; }
        
        [Required]
        public TypeRetenue TypeRetenue { get; set; }
        
        public StatutRetenue Statut { get; set; } = StatutRetenue.EnCours;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [Required]
        [MaxLength(200)]
        public string Beneficiaire { get; set; }
        
        [MaxLength(50)]
        public string? MatriculeFiscaleBeneficiaire { get; set; }
        
        [MaxLength(200)]
        public string? AdresseBeneficiaire { get; set; }
        
        // Informations du fichier scanné/uploadé (SEULEMENT date et chemin)
        public DateTime? DateScan { get; set; }
        
        [MaxLength(500)]
        public string? CheminFichier { get; set; }
        
        // Dates de gestion
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;
        
        public DateTime? DateModification { get; set; }
        
        // Relations
        [Required]
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        
        [Required]
        public Guid UtilisateurId { get; set; }
        public Utilisateur Utilisateur { get; set; }
        
        // Commentaires et notes
        public string? Commentaires { get; set; }
    }
}
