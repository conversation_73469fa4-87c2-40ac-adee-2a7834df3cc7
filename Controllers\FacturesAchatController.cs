using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace stage.Controllers
{
    [Route("api/factures-achat")]
    [ApiController]
    [Authorize] 
    public class FacturesAchatController : ControllerBase
    {
        private readonly IFactureAchatRepository _factureAchatRepository;
        private readonly IFournisseurRepository _fournisseurRepository;
        private readonly ISocieteRepository _societeRepository;

        public FacturesAchatController(
            IFactureAchatRepository factureAchatRepository, 
            IFournisseurRepository fournisseurRepository,
            ISocieteRepository societeRepository)
        {
            _factureAchatRepository = factureAchatRepository;
            _fournisseurRepository = fournisseurRepository;
            _societeRepository = societeRepository;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<FactureAchat>>> GetFacturesAchat()
        {
            var societeId = GetUserSocieteId();
            var factures = await _factureAchatRepository.GetFacturesAchatBySocieteIdAsync(societeId);
            return Ok(factures);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FactureAchat>> GetFactureAchat(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureAchatRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(facture);
        }

        [HttpPost]
        public async Task<ActionResult<FactureAchat>> PostFactureAchat([FromBody] FactureAchat facture)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();

            var fournisseur = await _fournisseurRepository.GetByIdAsync(facture.FournisseurId);
            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return BadRequest("Le fournisseur spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            var societe = await _societeRepository.GetByIdAsync(societeId);
            if (societe == null)
            {
                return BadRequest("Société non trouvée.");
            }

            facture.Id = Guid.NewGuid();
            facture.SocieteId = societeId; 
            facture.Date = DateTime.UtcNow;
            facture.MatriculeFiscaleSociete = societe.MatriculeFiscale;

            await _factureAchatRepository.AddAsync(facture);
            await _factureAchatRepository.SaveChangesAsync();

            var createdFacture = await _factureAchatRepository.GetByIdAsync(facture.Id);
            return CreatedAtAction(nameof(GetFactureAchat), new { id = createdFacture.Id }, createdFacture);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutFactureAchat(Guid id, [FromBody] FactureAchat facture)
        {
            if (id != facture.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingFacture = await _factureAchatRepository.GetByIdAsync(id);

            if (existingFacture == null || existingFacture.SocieteId != societeId)
            {
                return NotFound();
            }

            var fournisseur = await _fournisseurRepository.GetByIdAsync(facture.FournisseurId);
            if (fournisseur == null || fournisseur.SocieteId != societeId)
            {
                return BadRequest("Le fournisseur spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            existingFacture.Numero = facture.Numero;
            existingFacture.Montant = facture.Montant;
            existingFacture.FournisseurId = facture.FournisseurId;

            _factureAchatRepository.Update(existingFacture);

            try
            {
                await _factureAchatRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await FactureAchatExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFactureAchat(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureAchatRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            _factureAchatRepository.Remove(facture);
            await _factureAchatRepository.SaveChangesAsync();

            return NoContent();
        }

        private async Task<bool> FactureAchatExists(Guid id, Guid societeId)
        {
            return (await _factureAchatRepository.FindAsync(f => f.Id == id && f.SocieteId == societeId)).Any();
        }
    }
}
