﻿using stage.DTOs.Auth;
using stage.DTOs.User; 
using stage.Models;
using stage.Repositories;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity; 

namespace stage.Services
{
    public class AuthService : IAuthService
    {
        private readonly UserManager<Applicationuser> _userManager;
        private readonly SignInManager<Applicationuser> _signInManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager; 
        private readonly ISocieteRepository _societeRepository;
        private readonly IJwtService _jwtService;
        private readonly IRoleService _roleService;

        public AuthService(UserManager<Applicationuser> userManager,
                           SignInManager<Applicationuser> signInManager,
                           RoleManager<IdentityRole<Guid>> roleManager,
                           ISocieteRepository societeRepository,
                           IJwtService jwtService,
                           IRoleService roleService)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _roleManager = roleManager;
            _societeRepository = societeRepository;
            _jwtService = jwtService;
            _roleService = roleService;
        }

        public async Task<AuthResponseDto?> RegisterAdminAndSociete(UserRegisterDto registerDto)
        {
            Console.WriteLine($"Starting registration for email: {registerDto.Email}");

            var existingUser = await _userManager.FindByEmailAsync(registerDto.Email);
            if (existingUser != null)
            {
                Console.WriteLine($"Email {registerDto.Email} already exists");
                return null; 
            }

            Console.WriteLine("Email is available, proceeding with registration");

            var nouvelleSociete = new Societe
            {
                Id = Guid.NewGuid(),
                Nom = registerDto.SocieteNom,
                Adresse = registerDto.SocieteAdresse,
                MatriculeFiscale = registerDto.MatriculeFiscale,
                Signature = registerDto.Signature,
                Cachet = registerDto.Cachet,
                Logo = null
            };
            await _societeRepository.AddAsync(nouvelleSociete);

            var nouvelAdmin = new Applicationuser
            {
                Id = Guid.NewGuid(), 
                Nom = registerDto.Nom,
                Email = registerDto.Email,
                UserName = registerDto.Email, 
                SocieteId = nouvelleSociete.Id
            };

            var createResult = await _userManager.CreateAsync(nouvelAdmin, registerDto.MotDePasse);
            if (!createResult.Succeeded)
            {
                _societeRepository.Remove(nouvelleSociete);
                await _societeRepository.SaveChangesAsync();
                var errors = string.Join(", ", createResult.Errors.Select(e => e.Description));
                Console.WriteLine($"Error creating user: {errors}");
                foreach (var error in createResult.Errors)
                {
                    Console.WriteLine($"Identity Error - Code: {error.Code}, Description: {error.Description}");
                }
                return null;
            }

            await _roleService.InitializeDefaultRolesAsync();

            await _userManager.AddToRoleAsync(nouvelAdmin, RoleService.ADMIN_ROLE);

            await _societeRepository.SaveChangesAsync();

            var roles = await _userManager.GetRolesAsync(nouvelAdmin);
            var token = _jwtService.GenerateToken(nouvelAdmin, roles, nouvelAdmin.SocieteId);

            var userDto = new UtilisateurDto
            {
                Id = nouvelAdmin.Id,
                Nom = nouvelAdmin.Nom,
                Email = nouvelAdmin.Email,
                Role = roles.FirstOrDefault() ?? "N/A", 
                SocieteId = nouvelAdmin.SocieteId,
                SocieteNom = nouvelleSociete.Nom 
            };

            return new AuthResponseDto
            {
                Token = token,
                Message = "Inscription et société créées avec succès.",
                User = userDto
            };
        }

        public async Task<AuthResponseDto?> Login(UserLoginDto loginDto)
        {
            var user = await _userManager.FindByEmailAsync(loginDto.Email);

            if (user == null)
            {
                return null; 
            }

            var result = await _signInManager.CheckPasswordSignInAsync(user, loginDto.MotDePasse, lockoutOnFailure: false);

            if (!result.Succeeded)
            {
                return null; 
            }

        
            var roles = await _userManager.GetRolesAsync(user);
            var token = _jwtService.GenerateToken(user, roles, user.SocieteId);

            var societe = await _societeRepository.GetByIdAsync(user.SocieteId); 

            var userDto = new UtilisateurDto
            {
                Id = user.Id,
                Nom = user.Nom,
                Email = user.Email,
                Role = roles.FirstOrDefault() ?? "N/A", 
                SocieteId = user.SocieteId,
                SocieteNom = societe?.Nom
            };

            return new AuthResponseDto
            {
                Token = token,
                Message = "Connexion réussie.",
                User = userDto
            };
        }

        public Task<object> LogoutAsync()
        {

            Console.WriteLine($"User logged out at {DateTime.UtcNow}");



            return Task.FromResult<object>(new
            {
                Success = true,
                Message = "Déconnexion réussie. Veuillez supprimer le token de votre stockage local.",
                Timestamp = DateTime.UtcNow
            });
        }
    }
}