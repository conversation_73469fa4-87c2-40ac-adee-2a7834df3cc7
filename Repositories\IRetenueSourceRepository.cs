using stage.Models;

namespace stage.Repositories
{
    public interface IRetenueSourceRepository : IBaseRepository<RetenueSource>
    {
        Task<IEnumerable<RetenueSource>> GetBySocieteIdAsync(Guid societeId);
        Task<IEnumerable<RetenueSource>> GetByUtilisateurIdAsync(Guid utilisateurId);
        Task<IEnumerable<RetenueSource>> GetByDateRangeAsync(Guid societeId, DateTime dateDebut, DateTime dateFin);
        Task<IEnumerable<RetenueSource>> GetWithFilesAsync(Guid societeId); // Retenues avec fichiers
        Task<RetenueSource?> GetByNumeroAsync(string numero, Guid societeId);
        Task<IEnumerable<RetenueSource>> GetByTypeRetenueAsync(Guid societeId, TypeRetenue typeRetenue);
        Task<IEnumerable<RetenueSource>> GetByStatutAsync(Guid societeId, StatutRetenue statut);
        Task<decimal> GetTotalMontantRetenuAsync(Guid societeId, DateTime dateDebut, DateTime dateFin);
    }
}
