using Microsoft.EntityFrameworkCore;
using stage.Data;
using stage.Models;

namespace stage.Repositories
{
    public class RetenueSourceRepository : BaseRepository<RetenueSource>, IRetenueSourceRepository
    {
        public RetenueSourceRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<RetenueSource>> GetBySocieteIdAsync(Guid societeId)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Include(r => r.Societe)
                .Where(r => r.SocieteId == societeId)
                .OrderByDescending(r => r.DateCreation)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetByUtilisateurIdAsync(Guid utilisateurId)
        {
            return await _dbSet
                .Include(r => r.Societe)
                .Where(r => r.UtilisateurId == utilisateurId)
                .OrderByDescending(r => r.DateCreation)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetByDateRangeAsync(Guid societeId, DateTime dateDebut, DateTime dateFin)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Where(r => r.SocieteId == societeId && 
                           r.DateRetenue >= dateDebut && 
                           r.DateRetenue <= dateFin)
                .OrderByDescending(r => r.DateRetenue)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetScannedOrUploadedAsync(Guid societeId)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Where(r => r.SocieteId == societeId && r.EstScanneOuUploade)
                .OrderByDescending(r => r.DateCreation)
                .ToListAsync();
        }

        public async Task<RetenueSource?> GetByNumeroAsync(string numero, Guid societeId)
        {
            return await _dbSet
                .FirstOrDefaultAsync(r => r.Numero == numero && r.SocieteId == societeId);
        }

        public async Task<IEnumerable<RetenueSource>> GetByTypeRetenueAsync(Guid societeId, TypeRetenue typeRetenue)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Where(r => r.SocieteId == societeId && r.TypeRetenue == typeRetenue)
                .OrderByDescending(r => r.DateRetenue)
                .ToListAsync();
        }

        public async Task<IEnumerable<RetenueSource>> GetByStatutAsync(Guid societeId, StatutRetenue statut)
        {
            return await _dbSet
                .Include(r => r.Utilisateur)
                .Where(r => r.SocieteId == societeId && r.Statut == statut)
                .OrderByDescending(r => r.DateCreation)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalMontantRetenuAsync(Guid societeId, DateTime dateDebut, DateTime dateFin)
        {
            return await _dbSet
                .Where(r => r.SocieteId == societeId && 
                           r.DateRetenue >= dateDebut && 
                           r.DateRetenue <= dateFin &&
                           r.Statut == StatutRetenue.Valide)
                .SumAsync(r => r.MontantRetenu);
        }

        public override async Task<RetenueSource?> GetByIdAsync(Guid id)
        {
            return await _dbSet
                .Include(r => r.Societe)
                .Include(r => r.Utilisateur)
                .FirstOrDefaultAsync(r => r.Id == id);
        }
    }
}
