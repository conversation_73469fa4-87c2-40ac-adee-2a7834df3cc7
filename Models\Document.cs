using System.ComponentModel.DataAnnotations;

namespace stage.Models
{
    public enum SourceDocument
    {
        Generated,  // Document généré (attestations)
        Scanned,    // Document scanné/importé (factures, retenues)
        Uploaded    // Document uploadé manuellement
    }

    public enum StatutDocument
    {
        EnCours,        // En cours de traitement
        EnAttente,      // En attente de validation
        Valide,         // Validé et traité
        Rejete,         // Rejeté
        Archive         // Archivé
    }

    public class Document
    {
        public Guid Id { get; set; }

        [Required]
        [MaxLength(200)]
        public string Titre { get; set; }

        public string? Contenu { get; set; }

        public DateTime DateCreation { get; set; }

        public DateTime? DateModification { get; set; }

        // Type et source du document
        public Guid TypeDocumentId { get; set; }
        public TypeDocument TypeDocument { get; set; }

        [Required]
        public SourceDocument Source { get; set; }

        public StatutDocument Statut { get; set; } = StatutDocument.EnCours;

        // Informations de fichier (pour documents scannés/uploadés)
        public string? CheminFichier { get; set; }

        [MaxLength(100)]
        public string? NomFichierOriginal { get; set; }

        [MaxLength(50)]
        public string? TypeMime { get; set; }

        public long? TailleFichier { get; set; }

        // Métadonnées d'extraction OCR
        public string? DonneesExtraites { get; set; } // JSON des données OCR

        public bool? OcrTraite { get; set; }

        public DateTime? DateOcr { get; set; }

        public string? ErreurOcr { get; set; }

        // Relations
        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }

        // Utilisateur qui a créé/uploadé le document
        public Guid? UtilisateurId { get; set; }
        public Utilisateur? Utilisateur { get; set; }

        // Relation optionnelle avec FactureAchat (pour factures scannées)
        public Guid? FactureAchatId { get; set; }
        public FactureAchat? FactureAchat { get; set; }

        // Relation optionnelle avec RetenueSource (pour certificats de retenue scannés)
        public Guid? RetenueSourceId { get; set; }
        public RetenueSource? RetenueSource { get; set; }

        // Commentaires et notes
        public string? Commentaires { get; set; }

        // Métadonnées additionnelles (JSON pour flexibilité)
        public string? Metadonnees { get; set; }
    }
}